const express = require('express');
const fetch = require('node-fetch');
const rateLimit = require('../middleware/rateLimit');

const router = express.Router();

// Apply rate limiting to all routes
router.use(rateLimit);

/**
 * Stream chat response
 */
async function streamChatResponse(res, query, context, sessionId, requestStartTime, cacheService) {
  try {
    const openRouterService = require('../services/openRouterService');

    // Set headers for streaming
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('Access-Control-Allow-Origin', '*');

    // Send initial session info
    res.write(`data: ${JSON.stringify({
      type: 'session',
      sessionId,
      timestamp: new Date().toISOString()
    })}\n\n`);

    // Stream response from OpenRouter
    const streamGenerator = openRouterService.generateStreamingResponse(query, context);
    let fullResponse = '';

    for await (const chunk of streamGenerator) {
      fullResponse += chunk;
      res.write(`data: ${JSON.stringify({
        type: 'content',
        content: chunk
      })}\n\n`);
    }

    const totalDuration = Date.now() - requestStartTime;

    // Add to conversation history
    cacheService.addConversationEntry(sessionId, query, fullResponse, {
      contextLength: context.length,
      totalDuration: totalDuration
    });

    // Send completion signal
    res.write(`data: ${JSON.stringify({
      type: 'done',
      timestamp: new Date().toISOString(),
      timing: { total: totalDuration }
    })}\n\n`);

    res.end();

  } catch (error) {
    console.error('❌ Streaming error:', error.message);
    res.write(`data: ${JSON.stringify({
      type: 'error',
      message: 'An error occurred while generating the response'
    })}\n\n`);
    res.end();
  }
}

/**
 * Main API endpoint: /api/v1/
 * ONLY endpoint needed for ChatAI functionality
 * URL format: http://localhost:3002/api/v1/?apikey=test_api_key_1751884336144_vp9gospvg&query=invoice amount
 */
router.get('/api/v1/', async (req, res) => {
  const { apikey, query, sessionId, stream = 'true' } = req.query;

  // Validate required parameters
  if (!apikey) {
    return res.status(400).json({
      error: true,
      message: 'API key is required'
    });
  }

  if (!query) {
    return res.status(400).json({
      error: true,
      message: 'Query is required'
    });
  }

  const streamBool = stream === 'true' || stream === true;

  try {
    const userService = require('../services/userService');
    const origin = req.headers.origin || req.headers.referer || 'unknown';

    // Start timing
    const requestStartTime = Date.now();
    console.log(`⏱️ [TIMING] Request started at: ${new Date().toISOString()}`);

    // Step 1: Check cached API key validation first
    const cacheService = require('../services/cacheService');
    console.log('🔑 Checking API key validation cache...');

    let validationResult = cacheService.getCachedApiKeyValidation(apikey);
    let userServiceDuration = 0;

    if (!validationResult) {
      // Cache miss - call User Service key-validator (which now includes documents!)
      console.log(`🔄 USER SERVICE CALL - CACHE MISS - API KEY VALIDATION`);

      const userServiceStartTime = Date.now();
      validationResult = await userService.validateApiKey(apikey, origin);
      userServiceDuration = Date.now() - userServiceStartTime;

      console.log(`✅ USER SERVICE SUCCESS: API key validation completed in ${userServiceDuration}ms`);

      // Cache the validation result
      cacheService.cacheApiKeyValidation(apikey, { result: validationResult });
    } else {
      console.log(`⚡ Using cached API key validation (saved ~200-500ms)`);
      validationResult = validationResult.result;
    }

    // Extract data from validation result
    const chatAiData = validationResult;
    const appId = chatAiData.appId;
    const chatAiId = chatAiData.id;

    if (!appId || !chatAiId) {
      throw new Error('Invalid API key: missing appId or chatAiId');
    }

    console.log(`✅ API key validated successfully. AppId: ${appId}, ChatAiId: ${chatAiId}`);

    // Step 2: Import required services
    const llamaIndexService = require('../services/llamaIndexService');
    const openRouterService = require('../services/openRouterService');

    // Step 3: Get or create session using appId
    const currentSessionId = cacheService.getOrCreateSession(appId, sessionId);

    // Step 4: Use documents from key-validator response (OPTIMIZATION: no extra API call needed!)
    const documents = chatAiData.documents || [];
    console.log(`📄 Using documents from key-validator response: ${documents.length} documents available`);
    console.log(`⚡ OPTIMIZATION: Saved 1 API call by including documents in key-validator response`);

    // Cache the documents for future requests in this session
    cacheService.cacheDocuments(currentSessionId, appId, documents, null);

    // Step 5: Get context from LlamaIndex using documents
    const llamaIndexStartTime = Date.now();
    let finalContext = '';
    let llamaIndexDuration = 0;

    if (documents.length > 0) {
      const cachedContext = cacheService.getCachedContext(currentSessionId, query);

      if (cachedContext) {
        finalContext = cachedContext;
        console.log(`⚡ Using cached context (saved ~1-3s)`);
      } else {
        console.log(`🔍 Retrieving context from LlamaIndex for ${documents.length} documents...`);
        finalContext = await llamaIndexService.retrieveFromMultipleDocuments(documents, query);
        llamaIndexDuration = Date.now() - llamaIndexStartTime;

        // Cache the context
        cacheService.cacheContext(currentSessionId, query, finalContext);
        console.log(`✅ Context retrieved and cached in ${llamaIndexDuration}ms`);
      }
    } else {
      console.log(`⚠️ No documents available for context retrieval`);
    }

    // Step 6: Generate response
    if (streamBool) {
      await streamChatResponse(res, query, finalContext, currentSessionId, requestStartTime, cacheService);
    } else {
      const openRouterStartTime = Date.now();
      const response = await openRouterService.generateResponse(query, finalContext);
      const openRouterDuration = Date.now() - openRouterStartTime;

      // Add to conversation history
      cacheService.addConversationEntry(currentSessionId, query, response, {
        documentsUsed: documents.length,
        contextLength: finalContext.length,
        cached: {
          apiKey: userServiceDuration === 0,
          context: llamaIndexDuration === 0
        }
      });

      const totalDuration = Date.now() - requestStartTime;
      console.log(`⏱️ [TIMING] Total request completed in: ${totalDuration}ms`);

      res.json({
        error: false,
        sessionId: currentSessionId,
        response
      });
    }

  } catch (error) {
    console.error('❌ API v1 chat error:', error.message);

    // Return error in JSON format if headers not sent yet
    if (!res.headersSent) {
      const statusCode = error.message.includes('validation failed') ? 403 :
        error.message.includes('not found') ? 404 : 500;

      return res.status(statusCode).json({
        error: true,
        message: error.message
      });
    }
  }
});

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'ChatAI SDK Clean',
    endpoints: {
      main: '/api/v1/?apikey=...&query=...',
      health: '/health'
    }
  });
});

module.exports = router;
