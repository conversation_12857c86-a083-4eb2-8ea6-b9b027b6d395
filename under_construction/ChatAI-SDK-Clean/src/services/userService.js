const fetch = require('node-fetch');

class UserService {
  constructor() {
    const config = require('../config');
    this.baseUrl = config.userService.url;
    this.chatAiOrigin = config.chatAiOrigin;
  }

  /**
   * Validate API key using User-Service key-validator
   * This is the ONLY API call needed - it returns validation + documents in one call
   * @param {string} apiKey - Application API key
   * @param {string} origin - Request origin
   * @returns {Promise<Object>} Validation result with credit info and documents
   */
  async validateApiKey(apiKey, origin) {
    try {
      console.log(`\n🔗 ═══════════════════════════════════════════════════════════════`);
      console.log(`🔑 USER SERVICE CALL - API KEY VALIDATION`);
      console.log(`🗝️  API Key: ${apiKey ? `${apiKey.substring(0, 20)}...` : 'Not provided'}`);
      console.log(`🌐 Origin: ${origin}`);
      console.log(`🕐 Timestamp: ${new Date().toISOString()}`);
      console.log(`═══════════════════════════════════════════════════════════════\n`);

      const requestBody = {
        chainId: 1, // Default chainId for ChatAI
        apikey: apiKey,
        origin: origin,
        payload: {
          method: 'validate',
          params: []
        },
        type: 'chatai'
      };

      console.log(`🌐 Making request to: ${this.baseUrl}/users/app/key-validator`);
      console.log(`📤 Request method: POST`);
      console.log(`📦 Request payload: ${JSON.stringify(requestBody, null, 2)}`);

      const response = await fetch(
        `${this.baseUrl}/users/app/key-validator`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Origin': this.chatAiOrigin
          },
          body: JSON.stringify(requestBody)
        }
      );

      console.log(`📥 Response status: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.log(`❌ USER SERVICE ERROR: ${response.status} - ${errorText}`);
        throw new Error(`Key validation failed: ${response.status} - ${errorText}`);
      }

      const result = await response.json();

      if (result.error) {
        console.log(`❌ USER SERVICE ERROR: ${result.message || 'API key validation failed'}`);
        throw new Error(result.message || 'API key validation failed');
      }

      const credits = result.result.creditInfo?.creditsRemaining || 'Unknown';
      const subscriptionStatus = result.result.creditInfo?.subscriptionStatus || 'Unknown';
      const documentsCount = result.result.documents?.length || 0;

      console.log(`✅ USER SERVICE SUCCESS: API key validated successfully`);
      console.log(`💳 Credits remaining: ${credits}`);
      console.log(`📊 Subscription status: ${subscriptionStatus}`);
      console.log(`📄 Documents included: ${documentsCount} documents`);
      console.log(`⚡ OPTIMIZATION: Got validation + documents in single API call!`);
      console.log(`🔗 ═══════════════════════════════════════════════════════════════\n`);

      return result.result;

    } catch (error) {
      console.error(`❌ USER SERVICE ERROR - API KEY VALIDATION: ${error.message}`);
      console.log(`🔗 ═══════════════════════════════════════════════════════════════\n`);
      throw error;
    }
  }
}

module.exports = new UserService();
